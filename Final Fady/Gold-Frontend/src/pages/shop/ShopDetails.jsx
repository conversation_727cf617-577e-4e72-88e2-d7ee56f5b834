import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button.jsx';
import { Card, CardContent } from '@/components/ui/card.jsx';
import { Badge } from '@/components/ui/badge.jsx';
import { useAuth } from '../../context/AuthContext.jsx';
import {
    ArrowLeft,
    Star,
    MapPin,
    Calendar,
    Heart,
    Eye,
    ChevronLeft,
    ChevronRight,
    Phone,
    Clock,
    Shield
} from 'lucide-react';
import { shopService } from '../../services/shopService.js';
import { productService } from '../../services/productService.js';
import { rateService } from '../../services/rateService.js';
import { ROUTES } from '../../utils/constants.js';

// Custom CSS for animations
const customStyles = `
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
`;

// WhatsApp Icon Component
const WhatsAppIcon = ({ className = "w-5 h-5" }) => (
    <svg className={className} fill="currentColor" viewBox="0 0 24 24">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
    </svg>
);

// Shop Image Slider Component
const ShopImageSlider = ({ images = [], shopName }) => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const validImages = images.filter(img => img && typeof img === 'string' && img.trim() !== '');
    const displayImages = validImages.length > 0 ? validImages : [
        'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800&h=600&fit=crop&crop=center&auto=format&q=80'
    ];

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev + 1) % displayImages.length);
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev - 1 + displayImages.length) % displayImages.length);
    };

    useEffect(() => {
        const interval = setInterval(nextSlide, 4000);
        return () => clearInterval(interval);
    }, [displayImages.length]);

    return (
        <div className="relative w-full h-full overflow-hidden">
            {displayImages.map((image, index) => (
                <div
                    key={index}
                    className={`absolute inset-0 transition-opacity duration-1000 ${index === currentSlide ? 'opacity-100' : 'opacity-0'
                        }`}
                >
                    <img
                        src={image}
                        alt={`${shopName} - Image ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800&h=600&fit=crop&crop=center&auto=format&q=80';
                        }}
                    />
                </div>
            ))}

            {displayImages.length > 1 && (
                <>
                    <button
                        onClick={prevSlide}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-300 backdrop-blur-sm"
                    >
                        <ChevronLeft className="w-6 h-6" />
                    </button>
                    <button
                        onClick={nextSlide}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-300 backdrop-blur-sm"
                    >
                        <ChevronRight className="w-6 h-6" />
                    </button>

                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        {displayImages.map((_, index) => (
                            <button
                                key={index}
                                onClick={() => setCurrentSlide(index)}
                                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-white' : 'bg-white/50'
                                    }`}
                            />
                        ))}
                    </div>
                </>
            )}
        </div>
    );
};

const ShopDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { user } = useAuth();

    const [shop, setShop] = useState(null);
    const [products, setProducts] = useState([]);
    const [reviews, setReviews] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    const safeProducts = Array.isArray(products) ? products : [];
    const safeReviews = Array.isArray(reviews) ? reviews : [];

    useEffect(() => {
        if (id) {
            loadShopDetails();
        }
    }, [id]);

    const loadShopDetails = async () => {
        try {
            setIsLoading(true);
            console.log('🔄 Loading shop details for ID:', id);

            const [shopResponse, productsResponse, reviewsResponse] = await Promise.all([
                shopService.getShopById(id),
                productService.getProductsByShop(id),
                rateService.getShopRatings(id).catch(() => ({ data: [] }))
            ]);

            console.log('🏪 Shop response:', shopResponse);
            console.log('📦 Products response:', productsResponse);
            console.log('⭐ Reviews response:', reviewsResponse);

            if (shopResponse?.data) {
                setShop(shopResponse.data);
            }

            if (productsResponse?.data) {
                setProducts(Array.isArray(productsResponse.data) ? productsResponse.data : []);
            }

            if (reviewsResponse?.data) {
                setReviews(Array.isArray(reviewsResponse.data) ? reviewsResponse.data : []);
            }

        } catch (error) {
            console.error('❌ Error loading shop details:', error);
        } finally {
            setIsLoading(false);
        }
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB] flex items-center justify-center">
                <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-4 animate-spin">
                        <span className="text-white text-2xl">💎</span>
                    </div>
                    <p className="text-[#C37C00] font-semibold text-lg">Loading shop details...</p>
                </div>
            </div>
        );
    }

    if (!shop) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB] flex items-center justify-center">
                <div className="text-center">
                    <div className="w-24 h-24 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-6">
                        <span className="text-4xl">🏪</span>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Shop Not Found</h2>
                    <p className="text-gray-600 mb-6">The shop you're looking for doesn't exist or has been removed.</p>
                    <Button
                        onClick={() => navigate(ROUTES.SHOPS)}
                        className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-8 py-3 rounded-full font-semibold"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Shops
                    </Button>
                </div>
            </div>
        );
    }

    const safeShop = {
        name: shop.name || 'Unnamed Shop',
        description: shop.description || 'No description available',
        logoUrl: shop.logoUrl || '',
        images: shop.images || shop.gallery || [],
        address: shop.address || '',
        phone: shop.phone || '',
        whatsapp: shop.whatsapp || '',
        workingHours: shop.workingHours || 'Daily 9:00 AM - 10:00 PM',
        rating: shop.averageRating || shop.rating || 0,
        verified: shop.verified || false,
        status: shop.status || 'active',
        ownerName: shop.ownerName || 'Shop Owner',
        ...shop
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB]">
            {/* Inject custom styles */}
            <style>{customStyles}</style>

            {/* Back Button - Above Slider */}
            <div className="relative z-50 pt-20 pb-4 px-4 sm:px-6 lg:px-8">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(-1)}
                    className="bg-white/90 hover:bg-white text-[#C37C00] border border-[#C37C00]/30 backdrop-blur-md transition-all duration-300 rounded-full px-6 py-2 shadow-lg hover:shadow-xl"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Shops
                </Button>
            </div>

            {/* Image Slider */}
            <div className="relative h-96 md:h-[500px] lg:h-[600px] overflow-hidden">
                <ShopImageSlider
                    images={(() => {
                        const shopImages = [];
                        if (safeShop.logoUrl) {
                            shopImages.push(`${import.meta.env.VITE_API_BASE_URL}/shop-image/${safeShop.logoUrl}`);
                        }
                        if (Array.isArray(safeShop.gallery)) {
                            safeShop.gallery.forEach((img) => {
                                if (img && typeof img === 'string' && img.trim() !== '') {
                                    shopImages.push(`${import.meta.env.VITE_API_BASE_URL}/shop-image/${img}`);
                                }
                            });
                        }
                        if (Array.isArray(safeShop.images)) {
                            safeShop.images.forEach((img) => {
                                if (img && typeof img === 'string' && img.trim() !== '') {
                                    shopImages.push(`${import.meta.env.VITE_API_BASE_URL}/shop-image/${img}`);
                                }
                            });
                        }
                        return shopImages;
                    })()}
                    shopName={safeShop.name}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20"></div>
            </div>

            {/* Creative Shop Information Section */}
            <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
                <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden">
                    {/* Decorative Background Pattern */}
                    <div className="absolute inset-0 opacity-5">
                        <div className="absolute top-0 left-0 w-32 h-32 bg-[#C37C00] rounded-full -translate-x-16 -translate-y-16"></div>
                        <div className="absolute top-20 right-10 w-20 h-20 bg-[#A66A00] rounded-full animate-float"></div>
                        <div className="absolute bottom-10 left-20 w-24 h-24 bg-[#C37C00] rounded-full animate-float" style={{ animationDelay: '1s' }}></div>
                        <div className="absolute bottom-0 right-0 w-40 h-40 bg-[#A66A00] rounded-full translate-x-20 translate-y-20"></div>
                    </div>

                    {/* Header Section */}
                    <div className="relative bg-gradient-to-br from-[#C37C00] via-[#A66A00] to-[#8A5700] px-8 py-8">
                        {/* Floating Elements */}
                        <div className="absolute top-4 left-4 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
                        <div className="absolute top-8 right-8 w-12 h-12 bg-white/10 rounded-full animate-pulse" style={{ animationDelay: '300ms' }}></div>
                        <div className="absolute bottom-4 left-1/4 w-8 h-8 bg-white/10 rounded-full animate-pulse" style={{ animationDelay: '700ms' }}></div>

                        <div className="relative text-center">
                            {/* Premium Badge with Animation */}
                            <div className="inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm px-6 py-3 rounded-full text-white text-sm font-bold mb-6 shadow-xl border border-white/30 hover:scale-105 transition-transform duration-300">
                                <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center animate-spin-slow">
                                    <span className="text-white text-lg">👑</span>
                                </div>
                                <span className="text-lg">Premium Gold Shop</span>
                                <div className="w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
                            </div>

                            {/* Shop Name with Creative Typography */}
                            <div className="relative mb-6">
                                <h1 className="text-4xl md:text-6xl font-black text-white mb-2 leading-tight tracking-wide">
                                    <span className="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent drop-shadow-2xl">
                                        {safeShop.name}
                                    </span>
                                </h1>
                                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-white to-transparent rounded-full"></div>
                            </div>

                            {/* Shop Description with Style */}
                            <p className="text-xl text-white/90 font-medium mb-6 italic">
                                "shop for sell gold"
                            </p>

                            {/* Owner Info Card - Creative Design */}
                            <div className="inline-flex items-center gap-4 bg-white/15 backdrop-blur-md px-8 py-4 rounded-2xl border border-white/20 shadow-2xl hover:bg-white/20 transition-all duration-300 group">
                                {/* Owner Avatar */}
                                <div className="relative">
                                    <div className="w-16 h-16 bg-gradient-to-br from-white to-gray-200 rounded-full flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300">
                                        <span className="text-[#C37C00] font-black text-2xl">M</span>
                                    </div>
                                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                    </div>
                                </div>

                                {/* Owner Details */}
                                <div className="text-left">
                                    <h3 className="text-xl font-bold text-white mb-1 group-hover:text-yellow-200 transition-colors duration-300">
                                        Mustafa Lotfy Almanfaloty
                                    </h3>
                                    <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                                        <span className="text-white/80 font-medium">Verified Gold Merchant</span>
                                    </div>
                                </div>

                                {/* WhatsApp Button */}
                                <div className="ml-4">
                                    <Button
                                        onClick={() => window.open(`https://wa.me/+201234567890`, '_blank')}
                                        className="bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 group-hover:animate-bounce"
                                    >
                                        <WhatsAppIcon className="w-6 h-6" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Products Section */}
            <div className="px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white rounded-3xl shadow-2xl p-8">
                    <div className="text-center mb-8">
                        <div className="inline-flex items-center gap-3 mb-4">
                            <div className="w-10 h-10 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center animate-float">
                                <span className="text-white text-lg">💎</span>
                            </div>
                            <h2 className="text-3xl font-bold text-gray-900">Our Gold Collection</h2>
                        </div>
                        <p className="text-gray-600 text-lg mb-4">Discover our premium selection of gold jewelry</p>
                        <Badge className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] text-white px-6 py-2 text-lg font-bold rounded-full shadow-lg">
                            {safeProducts.length} Premium Products
                        </Badge>
                    </div>

                    {safeProducts.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {safeProducts.map((product) => (
                                <div key={product.id || product._id} className="product-card">
                                    <Card className="group overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white rounded-2xl">
                                        {/* Product Image */}
                                        <div className="relative overflow-hidden">
                                            <img
                                                src={`${import.meta.env.VITE_API_BASE_URL}/product-image/${product.logoUrl}`}
                                                alt={product.title || product.name}
                                                className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                                                onError={(e) => {
                                                    e.target.src = 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=300&fit=crop&crop=center&auto=format&q=60';
                                                }}
                                            />

                                            {/* Overlay on hover */}
                                            <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                                <Button
                                                    onClick={() => navigate(ROUTES.PRODUCT_DETAILS(product.id || product._id))}
                                                    className="bg-white text-[#C37C00] hover:bg-[#C37C00] hover:text-white px-6 py-2 rounded-full font-semibold transition-all duration-300 transform scale-90 group-hover:scale-100"
                                                >
                                                    View Details
                                                </Button>
                                            </div>
                                        </div>

                                        {/* Product Name */}
                                        <CardContent className="p-4">
                                            <div className="text-center">
                                                <h3 className="font-bold text-lg text-gray-900 group-hover:text-[#C37C00] transition-colors duration-300 line-clamp-2">
                                                    {String(product.title || product.name || 'Untitled Product')}
                                                </h3>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <div className="w-24 h-24 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-6 animate-float">
                                <span className="text-4xl">📦</span>
                            </div>
                            <h3 className="text-2xl font-bold text-gray-900 mb-4">No Products Available</h3>
                            <p className="text-gray-600 text-lg">This shop hasn't added any products yet. Check back later!</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ShopDetails;
