import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { useAuth } from '../../context/AuthContext.jsx';
import {
    ArrowLeft,
    Star,
    MapPin,
    Calendar,
    Heart,
    Eye,
    ChevronLeft,
    ChevronRight,
    Phone,
    Clock,
    Shield,
    ShoppingBag,
    Users,
    Award,
    Grid3X3,
    List
} from 'lucide-react';
import { shopService } from '../../services/shopService';
import { productService } from '../../services/productService';
import { ROUTES } from '../../utils/constants';

// WhatsApp Icon Component
const WhatsAppIcon = ({ className = "w-5 h-5" }) => (
    <svg className={className} fill="currentColor" viewBox="0 0 24 24">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
    </svg>
);

// Shop Image Slider Component
const ShopImageSlider = ({ images = [], shopName }) => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const validImages = images.filter(img => img && typeof img === 'string' && img.trim() !== '');
    const displayImages = validImages.length > 0 ? validImages : [
        'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800&h=600&fit=crop&crop=center&auto=format&q=80'
    ];

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev + 1) % displayImages.length);
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev - 1 + displayImages.length) % displayImages.length);
    };


    useEffect(() => {
        const interval = setInterval(nextSlide, 4000);
        return () => clearInterval(interval);
    }, [displayImages.length]);

    return (
        <div className="relative w-full h-full overflow-hidden">
            {displayImages.map((image, index) => (
                <div
                    key={index}
                    className={`absolute inset-0 transition-opacity duration-1000 ${index === currentSlide ? 'opacity-100' : 'opacity-0'
                        }`}
                >
                    <img
                        src={image}
                        alt={`${shopName} - Image ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=800&h=600&fit=crop&crop=center&auto=format&q=80';
                        }}
                    />
                </div>
            ))}

            {displayImages.length > 1 && (
                <>
                    <button
                        onClick={prevSlide}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-300 backdrop-blur-sm"
                    >
                        <ChevronLeft className="w-6 h-6" />
                    </button>
                    <button
                        onClick={nextSlide}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-300 backdrop-blur-sm"
                    >
                        <ChevronRight className="w-6 h-6" />
                    </button>

                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        {displayImages.map((_, index) => (
                            <button
                                key={index}
                                onClick={() => setCurrentSlide(index)}
                                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-white' : 'bg-white/50'
                                    }`}
                            />
                        ))}
                    </div>
                </>
            )}
        </div>
    );
};

const ShopDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { user } = useAuth();

    const [shop, setShop] = useState(null);
    const [products, setProducts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [viewMode, setViewMode] = useState('grid');
    const { isAuthenticated, isRegularUser } = useAuth();

    const safeProducts = Array.isArray(products) ? products : [];

    useEffect(() => {
        if (id) {
            loadShopDetails();
        }
    }, [id]);

    const loadShopDetails = async () => {
        try {
            setIsLoading(true);
            console.log('🔄 Loading shop details for ID:', id);

            // Load shop details
            const shopResponse = await shopService.getShop(id);
            const shopData = shopResponse.data || shopResponse;

            if (shopData) {
                setShop(shopData);
            }

            // Load products
            try {
                const productsResponse = await productService.getProductsByShop(id);
                const productsData = Array.isArray(productsResponse)
                    ? productsResponse
                    : productsResponse.data || [];
                setProducts(productsData);
            } catch (error) {
                console.error('Error loading products:', error);
                setProducts([]);
            }

        } catch (error) {
            console.error('❌ Error loading shop details:', error);
            setShop(null);
        } finally {
            setIsLoading(false);
        }
    };

    const handleBookAppointment = () => {
        if (!user) {
            alert('Please login first to book an appointment');
            navigate(ROUTES.LOGIN);
            return;
        }
        navigate(ROUTES.BOOK_APPOINTMENT(id));
    };

    const handleAddToFavorites = async (productId) => {
        if (!user) {
            alert('Please login first to add product to favorites');
            navigate(ROUTES.LOGIN);
            return;
        }

        try {
            await productService.addToFavorites(productId);
            setProducts(prev => prev.map(product => {
                const currentProductId = product.id || product._id;
                return currentProductId === productId
                    ? { ...product, isFavorited: true }
                    : product;
            }));
            alert('Product added to favorites successfully!');
        } catch (error) {
            console.error('Error adding to favorites:', error);
            alert('Error adding product to favorites');
        }
    };

    // Product Card Component
    const ProductCard = ({ product }) => {
        const productId = product.id || product._id;

        const defaultGoldImages = [
            'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=300&fit=crop&crop=center&auto=format&q=60',
            'https://images.unsplash.com/photo-1611652022419-a9419f74343d?w=400&h=300&fit=crop&crop=center&auto=format&q=60',
            'https://images.unsplash.com/photo-1602173574767-37ac01994b2a?w=400&h=300&fit=crop&crop=center&auto=format&q=60',
            'https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?w=400&h=300&fit=crop&crop=center&auto=format&q=60',
            'https://images.unsplash.com/photo-1573408301185-9146fe634ad0?w=400&h=300&fit=crop&crop=center&auto=format&q=60'
        ];

        const defaultProductImage = defaultGoldImages[productId ? (productId.length % defaultGoldImages.length) : 0];

        return (
            <Card
                className="group hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 cursor-pointer overflow-hidden border-0 bg-white rounded-3xl shadow-lg h-[500px] flex flex-col"
                onClick={() => {
                    if (productId) {
                        navigate(ROUTES.PRODUCT_DETAILS(productId));
                    }
                }}
            >
                <div className="relative overflow-hidden h-64">
                    <img
                        src={`${import.meta.env.VITE_API_BASE_URL}/product-image/${product.logoUrl}`}
                        alt={product.name || 'Product'}
                        className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-500"
                        onError={(e) => {
                            e.target.src = defaultProductImage;
                        }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <Button
                        size="sm"
                        variant="ghost"
                        className="absolute top-3 right-3 bg-white/90 hover:bg-white backdrop-blur-sm rounded-full w-9 h-9 p-0 shadow-md opacity-0 group-hover:opacity-100 transition-all duration-300"
                        onClick={(e) => {
                            e.stopPropagation();
                            handleAddToFavorites(productId);
                        }}
                    >
                        <Heart className={`w-4 h-4 ${product.isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
                    </Button>

                    <Badge className="absolute top-3 left-3 bg-[#C37C00] hover:bg-[#A66A00] text-white opacity-0 group-hover:opacity-100 transition-all duration-300">
                        Available
                    </Badge>
                </div>

                <CardContent className="p-5 flex-1 flex flex-col">
                    <div className="space-y-3 flex-1">
                        <div className="space-y-2">
                            <h3 className="font-bold text-lg text-gray-900 group-hover:text-[#C37C00] transition-colors duration-300 line-clamp-2 leading-tight min-h-[56px]">
                                {product.name || product.title || 'Untitled Product'}
                            </h3>

                            {product.description && (
                                <p className="text-gray-600 text-sm leading-relaxed line-clamp-2 min-h-[40px]">
                                    {product.description}
                                </p>
                            )}
                        </div>

                        <div className="flex items-center justify-center py-2">
                            <div className="flex items-center gap-2 bg-[#F8F4ED] px-4 py-2 rounded-full border border-[#E2D2B6]/50">
                                <div className="flex">
                                    {[...Array(5)].map((_, i) => (
                                        <Star
                                            key={i}
                                            className={`w-4 h-4 ${i < Math.floor(product.rating || 0)
                                                ? 'fill-[#C37C00] text-[#C37C00]'
                                                : 'text-gray-300'
                                                }`}
                                        />
                                    ))}
                                </div>
                                <span className="text-sm font-semibold text-gray-800">
                                    {typeof product.rating === 'number' ? product.rating.toFixed(1) : '0.0'}
                                </span>
                                <span className="text-xs text-gray-500">
                                    ({product.reviewCount || product.reviews?.length || 0})
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="mt-auto pt-3 border-t border-gray-100">
                        <Button
                            size="sm"
                            className="w-full bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white rounded-full py-2.5 text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                            onClick={(e) => {
                                e.stopPropagation();
                                if (productId) {
                                    navigate(ROUTES.PRODUCT_DETAILS(productId));
                                }
                            }}
                        >
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB] flex items-center justify-center">
                <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-4 animate-spin">
                        <span className="text-white text-2xl">💎</span>
                    </div>
                    <p className="text-[#C37C00] font-semibold text-lg">Loading shop details...</p>
                </div>
            </div>
        );
    }

    if (!shop) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB] flex items-center justify-center">
                <div className="text-center">
                    <div className="w-24 h-24 bg-gradient-to-r from-[#C37C00] to-[#A66A00] rounded-full flex items-center justify-center mx-auto mb-6">
                        <span className="text-4xl">🏪</span>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Shop Not Found</h2>
                    <p className="text-gray-600 mb-6">The shop you're looking for doesn't exist or has been removed.</p>
                    <Button
                        onClick={() => navigate(ROUTES.SHOPS)}
                        className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-8 py-3 rounded-full font-semibold"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Shops
                    </Button>
                </div>
            </div>
        );
    }

    const safeShop = {
        name: shop.name || 'Unnamed Shop',
        description: shop.description || 'No description available',
        logoUrl: shop.logoUrl || '',
        images: shop.images || shop.gallery || [],
        address: shop.address || '',
        phone: shop.phone || '',
        whatsapp: shop.whatsapp || '',
        workingHours: shop.workingHours || 'Daily 9:00 AM - 10:00 PM',
        rating: shop.averageRating || shop.rating || 0,
        verified: shop.verified || false,
        status: shop.status || 'active',
        ownerName: shop.ownerName || 'Shop Owner',
        ...shop
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-[#F8F4ED] via-white to-[#F0E8DB]">
            {/* Back Button */}
            <div className="relative z-50 pt-20 pb-4 px-4 sm:px-6 lg:px-8">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(-1)}
                    className="bg-white/90 hover:bg-white text-[#C37C00] border border-[#C37C00]/30 backdrop-blur-md transition-all duration-300 rounded-full px-6 py-2 shadow-lg hover:shadow-xl"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Shops
                </Button>
            </div>

            {/* Image Slider */}
            <div className="relative h-96 md:h-[500px] lg:h-[600px] overflow-hidden">
                <ShopImageSlider
                    images={(() => {
                        const shopImages = [];
                        if (safeShop.logoUrl) {
                            shopImages.push(`${import.meta.env.VITE_API_BASE_URL}/shop-image/${safeShop.logoUrl}`);
                        }
                        if (Array.isArray(safeShop.gallery)) {
                            safeShop.gallery.forEach((img) => {
                                if (img && typeof img === 'string' && img.trim() !== '') {
                                    shopImages.push(`${import.meta.env.VITE_API_BASE_URL}/shop-image/${img}`);
                                }
                            });
                        }
                        if (Array.isArray(safeShop.images)) {
                            safeShop.images.forEach((img) => {
                                if (img && typeof img === 'string' && img.trim() !== '') {
                                    shopImages.push(`${import.meta.env.VITE_API_BASE_URL}/shop-image/${img}`);
                                }
                            });
                        }
                        return shopImages;
                    })()}
                    shopName={safeShop.name}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20"></div>

                {/* Shop Info Overlay */}
                <div className="absolute bottom-0 left-0 right-0 p-8">
                    <div className="flex items-end justify-between">
                        <div className="text-white">
                            <div className="flex items-center gap-3 mb-3">
                                <h1 className="text-4xl md:text-5xl font-bold">{safeShop.name}</h1>
                                {safeShop.verified && (
                                    <Badge className="bg-green-500 hover:bg-green-600 flex items-center gap-1">
                                        <Shield className="w-3 h-3" />
                                        Verified
                                    </Badge>
                                )}
                            </div>
                            <div className="flex items-center gap-6 text-white/90">
                                <div className="flex items-center gap-2">
                                    <div className="flex">
                                        {[...Array(5)].map((_, i) => (
                                            <Star
                                                key={i}
                                                className={`w-5 h-5 ${i < Math.floor(safeShop.rating)
                                                    ? 'fill-[#C37C00] text-[#C37C00]'
                                                    : 'text-white/40'
                                                    }`}
                                            />
                                        ))}
                                    </div>
                                    <span className="text-lg font-semibold">
                                        {safeShop.rating ? safeShop.rating.toFixed(1) : '0.0'}
                                    </span>
                                    <span className="text-sm">
                                        (0 reviews)
                                    </span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Award className="w-4 h-4" />
                                    <span className="text-sm">
                                        Since {safeShop.established || safeShop.createdAt ?
                                            new Date(safeShop.established || safeShop.createdAt).getFullYear() :
                                            'N/A'
                                        }
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Shop Info Section */}
            <div className="bg-white shadow-xl mb-8 overflow-hidden">
                <div className="p-8">
                    {/* Contact Information Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
                        <Card className="border-0 bg-gradient-to-r from-[#F0E8DB] to-[#E2D2B6] hover:shadow-lg transition-shadow rounded-2xl">
                            <CardContent className="p-6 flex items-center gap-4">
                                <div className="p-3 bg-[#C37C00] rounded-full">
                                    <MapPin className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm text-[#C37C00] font-medium mb-1">Address</p>
                                    <p className="text-gray-800 font-semibold text-base">{safeShop.address}</p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 bg-gradient-to-r from-indigo-50 to-indigo-100 hover:shadow-lg transition-shadow rounded-2xl">
                            <CardContent className="p-6 flex items-center gap-4">
                                <div className="p-3 bg-indigo-500 rounded-full">
                                    <Phone className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm text-indigo-600 font-medium mb-1">Phone</p>
                                    <p className="text-gray-800 font-semibold text-base">
                                        <a
                                            href={`tel:${safeShop.phone}`}
                                            className="hover:text-indigo-600 transition-colors"
                                        >
                                            {safeShop.phone}
                                        </a>
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 bg-gradient-to-r from-green-50 to-green-100 hover:shadow-lg transition-shadow rounded-2xl">
                            <CardContent className="p-6 flex items-center gap-4">
                                <div className="p-3 bg-green-500 rounded-full">
                                    <WhatsAppIcon className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm text-green-600 font-medium mb-1">WhatsApp</p>
                                    <p className="text-gray-800 font-semibold text-base">
                                        <a
                                            href={`https://wa.me/${safeShop.phone}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="hover:text-green-600 transition-colors"
                                        >
                                            {safeShop.phone}
                                        </a>
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-0 bg-gradient-to-r from-purple-50 to-purple-100 hover:shadow-lg transition-shadow rounded-2xl">
                            <CardContent className="p-6 flex items-center gap-4">
                                <div className="p-3 bg-purple-500 rounded-full">
                                    <Clock className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <p className="text-sm text-purple-600 font-medium mb-1">Working Hours</p>
                                    <p className="text-gray-800 font-semibold text-base">{safeShop.workingHours}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Description */}
                    <div className="mb-10">
                        <h3 className="text-2xl font-bold text-gray-900 mb-6">About Shop</h3>
                        <p className="text-gray-700 leading-relaxed text-xl bg-gradient-to-br from-[#F8F4ED] to-[#F0E8DB] p-6 rounded-2xl border border-[#E2D2B6]/30">
                            {safeShop.description}
                        </p>
                    </div>

                    {/* Action Section */}
                    {(isAuthenticated && user && (isRegularUser)) && (
                        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 p-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl">
                            <div className="flex gap-6">
                                <Button
                                    size="lg"
                                    onClick={handleBookAppointment}
                                    className="bg-gradient-to-r from-[#C37C00] to-[#A66A00] hover:from-[#A66A00] hover:to-[#8A5700] text-white px-10 py-4 rounded-full font-bold text-lg shadow-xl hover:shadow-2xl transition-all"
                                >
                                    <Calendar className="w-6 h-6 mr-3" />
                                    Book Appointment
                                </Button>
                            </div>

                            {/* Stats */}
                            <div className="flex gap-10">
                                {/* <div className="text-center">
                                <div className="flex items-center justify-center w-16 h-16 bg-[#C37C00] rounded-full mb-3 shadow-lg">
                                    <ShoppingBag className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold text-gray-900">{safeProducts.length}</div>
                                <div className="text-base text-gray-600 font-medium">Products</div>
                            </div>
                            <div className="text-center">
                                <div className="flex items-center justify-center w-16 h-16 bg-[#A66A00] rounded-full mb-3 shadow-lg">
                                    <Users className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold text-gray-900">
                                    {safeShop.customersCount || safeShop.customerCount || 0}
                                </div>
                                <div className="text-base text-gray-600 font-medium">Customers</div>
                            </div>
                            <div className="text-center">
                                <div className="flex items-center justify-center w-16 h-16 bg-[#8A5700] rounded-full mb-3 shadow-lg">
                                    <Star className="w-8 h-8 text-white" />
                                </div>
                                <div className="text-3xl font-bold text-gray-900">0</div>
                                <div className="text-base text-gray-600 font-medium">Reviews</div>
                            </div> */}
                            </div>
                        </div>)}
                </div>
            </div>

            {/* Products Section */}
            <div className="bg-white shadow-xl overflow-hidden">
                <div className="px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
                    <div className="flex items-center justify-between mb-10">
                        <div>
                            <h2 className="text-4xl font-bold text-gray-900 mb-3">Shop Products</h2>
                            <p className="text-gray-600 text-lg">Discover our exclusive collection</p>
                        </div>
                        <div className="flex items-center gap-4">
                            <div className="flex bg-[#F0E8DB] rounded-full p-2 border border-[#E2D2B6]/50">
                                <Button
                                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setViewMode('grid')}
                                    className={`p-2 ${viewMode === 'grid' ? 'bg-[#C37C00] text-white' : 'text-gray-600'}`}
                                >
                                    <Grid3X3 className="w-4 h-4" />
                                </Button>
                                <Button
                                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                                    size="sm"
                                    onClick={() => setViewMode('list')}
                                    className={`p-2 ${viewMode === 'list' ? 'bg-[#C37C00] text-white' : 'text-gray-600'}`}
                                >
                                    <List className="w-4 h-4" />
                                </Button>
                            </div>
                        </div>
                    </div>

                    {safeProducts.length === 0 ? (
                        <div className="text-center py-20 bg-gradient-to-br from-white to-[#F8F4ED]/50 rounded-2xl shadow-lg border border-[#E2D2B6]/30">
                            <div className="text-8xl mb-6">📦</div>
                            <h3 className="text-2xl font-bold text-gray-900 mb-4">No products found</h3>
                            <p className="text-gray-600 text-lg mb-8 max-w-md mx-auto">
                                This shop hasn't added any products yet. Check back later!
                            </p>
                        </div>
                    ) : (
                        <div className={`grid ${viewMode === 'grid'
                            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8'
                            : 'grid-cols-1 gap-6'
                            }`}>
                            {safeProducts.map((product) => (
                                <ProductCard key={product.id || product._id} product={product} />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ShopDetails;
